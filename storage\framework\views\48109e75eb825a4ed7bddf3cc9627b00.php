<?php $__env->startSection('content'); ?>
<div class="auth-wrapper <?php echo e($userType === 'department' ? 'department-auth' : ($userType === 'ucua' ? 'ucua-auth' : ($userType === 'admin' ? 'admin-auth' : 'user-auth'))); ?>">
    <div class="split-container">
        <!-- Left Panel -->
        <div class="left-panel">
        </div>

        <!-- Right Panel -->
        <div class="right-panel">
            <div class="auth-card">
                <div class="brand-header">
                    <div class="logo-container">
                        <img src="<?php echo e(asset('images/logo.png')); ?>" alt="UCUA Logo" height="40">
                    </div>
                </div>
                <h3 class="text-center fw-bold mb-4">Enter Login OTP</h3>
                <p class="text-center text-muted mb-4">
                    We've sent a verification code to your email address. Please enter it below to complete your login.
                </p>

                <?php if(session('status')): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo e(session('status')); ?>

                    </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo e(route('login.otp.verify')); ?>" data-ucua-form data-ucua-options='{"loadingText": "Verifying OTP..."}'>
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="email" value="<?php echo e($email); ?>">
                    <input type="hidden" name="user_type" value="<?php echo e($userType); ?>">

                    <div class="form-floating mb-3">
                        <input id="otp" type="text" 
                            class="form-control <?php $__errorArgs = ['otp'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            name="otp" required autofocus maxlength="6"
                            placeholder="Enter OTP">
                        <label for="otp">One-Time Password (OTP)</label>
                        <?php $__errorArgs = ['otp'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="invalid-feedback" role="alert">
                                <strong><?php echo e($message); ?></strong>
                            </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="alert alert-info mb-4">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            The OTP will expire in 5 minutes for security purposes.
                        </small>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 py-3 mb-4">
                        <i class="fas fa-shield-alt me-2"></i>Verify OTP & Login
                    </button>
                </form>

                <div class="text-center mb-0">
                    <p class="mb-2">Didn't receive the OTP?</p>
                    <form method="POST" action="<?php echo e(route('login.otp.resend')); ?>" class="d-inline">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="email" value="<?php echo e($email); ?>">
                        <input type="hidden" name="user_type" value="<?php echo e($userType); ?>">
                        <button type="submit" class="btn btn-link text-primary text-decoration-none fw-bold p-0">
                            <i class="fas fa-redo me-1"></i>Resend OTP
                        </button>
                    </form>
                </div>

                <hr class="my-4">

                <div class="text-center">
                    <?php if($userType === 'admin'): ?>
                        <a href="<?php echo e(route('admin.login')); ?>" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i> Back to Admin Login
                        </a>
                    <?php elseif($userType === 'ucua'): ?>
                        <a href="<?php echo e(route('ucua.login')); ?>" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i> Back to UCUA Login
                        </a>
                    <?php elseif($userType === 'department'): ?>
                        <a href="<?php echo e(route('department.login')); ?>" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i> Back to Department Login
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i> Back to User Login
                        </a>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </div>
    
    <footer class="auth-footer">
        <p class="mb-0">&copy; <?php echo e(date('Y')); ?> Copyright: Nursyahmina Mosdy, Dr Cik Feresa Mohd Foozy</p>
    </footer>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on OTP input
    document.getElementById('otp').focus();
    
    // Auto-submit when 6 characters are entered
    document.getElementById('otp').addEventListener('input', function() {
        if (this.value.length === 6) {
            // Optional: Auto-submit after a short delay
            // setTimeout(() => this.form.submit(), 500);
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ucua-fyp\resources\views/auth/login-otp-form.blade.php ENDPATH**/ ?>